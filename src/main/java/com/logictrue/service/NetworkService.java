package com.logictrue.service;

import com.alibaba.fastjson2.JSON;
import com.logictrue.config.ConfigManager;
import com.logictrue.event.EventBus;
import com.logictrue.util.PathUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 网络服务类，处理HTTP请求和文件下载
 */
public class NetworkService {
    private static final Logger logger = LoggerFactory.getLogger(NetworkService.class);
    private static final String IMAGES_DIR;

    private HttpClient httpClient;
    private ConfigManager configManager;

    static {
        // 获取jar文件所在目录
        String jarDir = PathUtils.getJarDirectory();
        IMAGES_DIR = jarDir + File.separator + "images";
    }

    public NetworkService() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(5))
                .build();
        this.configManager = ConfigManager.getInstance();

        // 创建图片目录
        createImagesDirectory();
    }



    /**
     * 创建图片存储目录
     */
    private void createImagesDirectory() {
        try {
            Path imagesPath = Paths.get(IMAGES_DIR);
            if (!Files.exists(imagesPath)) {
                Files.createDirectories(imagesPath);
                logger.info("创建图片目录成功: {}", IMAGES_DIR);
            }
        } catch (IOException e) {
            logger.error("创建图片目录失败: {}", IMAGES_DIR, e);
        }
    }

    /**
     * 异步下载设备图片
     */
    public CompletableFuture<String> downloadDeviceImage(String imageUrl) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (imageUrl == null || imageUrl.trim().isEmpty()) {
                    logger.error("图片URL为空，无法下载");
                    return null;
                }

                // 从URL中提取文件扩展名
                String fileExtension = extractFileExtension(imageUrl);
                String fileName = "device_" + UUID.randomUUID().toString().substring(0, 8) + fileExtension;
                String localPath = IMAGES_DIR + File.separator + fileName;

                logger.info("开始下载设备图片: {} -> {}", imageUrl, localPath);

                // 确保目录存在
                createImagesDirectory();

                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(imageUrl))
                        .timeout(Duration.ofSeconds(30))
                        .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                        .GET()
                        .build();

                HttpResponse<InputStream> response = httpClient.send(request,
                        HttpResponse.BodyHandlers.ofInputStream());

                logger.info("HTTP响应状态码: {}", response.statusCode());

                if (response.statusCode() == 200) {
                    File localFile = new File(localPath);
                    try (InputStream inputStream = response.body();
                         FileOutputStream outputStream = new FileOutputStream(localFile)) {

                        byte[] buffer = new byte[8192]; // 增大缓冲区
                        int bytesRead;
                        long totalBytes = 0;

                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            outputStream.write(buffer, 0, bytesRead);
                            totalBytes += bytesRead;
                        }

                        logger.info("设备图片下载成功: {}, 文件大小: {} bytes", localPath, totalBytes);

                        // 验证文件是否成功保存
                        if (localFile.exists() && localFile.length() > 0) {
                            // 下载成功后，将图片路径写入配置文件
                            updateBackgroundImageConfig(localPath);
                            return localPath;
                        } else {
                            logger.error("下载的图片文件为空或不存在: {}", localPath);
                            return null;
                        }
                    }
                } else {
                    logger.error("下载设备图片失败，HTTP状态码: {}, URL: {}", response.statusCode(), imageUrl);
                    return null;
                }
            } catch (java.net.ConnectException e) {
                logger.error("无法连接到图片服务器, URL: {}, 错误: 连接被拒绝或服务器未运行", imageUrl);
                return null;
            } catch (java.net.UnknownHostException e) {
                logger.error("无法解析主机名, URL: {}, 错误: {}", imageUrl, e.getMessage());
                return null;
            } catch (java.net.SocketTimeoutException e) {
                logger.error("连接超时, URL: {}, 错误: {}", imageUrl, e.getMessage());
                return null;
            } catch (Exception e) {
                logger.error("下载设备图片异常, URL: {}, 错误类型: {}, 错误信息: {}",
                           imageUrl, e.getClass().getSimpleName(), e.getMessage(), e);
                return null;
            }
        });
    }

    /**
     * 从URL中提取文件扩展名
     */
    private String extractFileExtension(String imageUrl) {
        try {
            // 移除查询参数
            String urlWithoutParams = imageUrl.split("\\?")[0];

            // 获取文件名部分
            String fileName = urlWithoutParams.substring(urlWithoutParams.lastIndexOf('/') + 1);

            // 提取扩展名
            int dotIndex = fileName.lastIndexOf('.');
            if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
                String extension = fileName.substring(dotIndex).toLowerCase();
                // 验证是否为常见图片格式
                if (extension.matches("\\.(jpg|jpeg|png|gif|bmp|webp)")) {
                    return extension;
                }
            }
        } catch (Exception e) {
            logger.debug("提取文件扩展名失败，使用默认扩展名: {}", imageUrl, e);
        }

        // 默认使用.jpg扩展名
        return ".jpg";
    }

    /**
     * 更新背景图片配置
     * 下载图片成功后，将图片路径写入配置文件，并优先使用下载的图片作为背景
     */
    private void updateBackgroundImageConfig(String imagePath) {
        try {
            // 获取当前配置的背景图片路径
            String currentBackgroundPath = configManager.getBackgroundImagePath();

            // 如果当前没有设置背景图片，或者当前背景图片不存在，则更新为新下载的图片
            boolean shouldUpdate = false;

            if (currentBackgroundPath == null || currentBackgroundPath.trim().isEmpty()) {
                // 当前没有设置背景图片
                shouldUpdate = true;
                logger.info("当前未设置背景图片，将使用新下载的图片: {}", imagePath);
            } else {
                // 检查当前背景图片文件是否存在
                File currentBackgroundFile = new File(currentBackgroundPath);
                if (!currentBackgroundFile.exists()) {
                    shouldUpdate = true;
                    logger.info("当前背景图片文件不存在: {}，将使用新下载的图片: {}", currentBackgroundPath, imagePath);
                } else {
                    // 当前背景图片存在，但优先使用新下载的图片
                    shouldUpdate = true;
                    logger.info("更新背景图片为新下载的图片: {} -> {}", currentBackgroundPath, imagePath);
                }
            }

            if (shouldUpdate) {
                // 更新配置文件中的背景图片路径
                configManager.setBackgroundImagePath(imagePath);
                logger.info("背景图片配置已更新: {}", imagePath);

                // 通知主页更新背景图片（如果主页控制器可用）
                notifyMainControllerToUpdateBackground(imagePath);
            }
        } catch (Exception e) {
            logger.error("更新背景图片配置失败", e);
        }
    }

    /**
     * 通知主页控制器更新背景图片
     * 使用事件总线机制通知主页控制器立即更新背景图片
     */
    private void notifyMainControllerToUpdateBackground(String imagePath) {
        try {
            logger.info("通过事件总线通知主页更新背景图片: {}", imagePath);

            // 使用事件总线发布背景图片更新事件
            EventBus.getInstance().publishBackgroundImageUpdate(imagePath);

        } catch (Exception e) {
            logger.error("通知主页更新背景图片失败", e);
        }
    }

    /**
     * 异步发送心跳请求
     */
    public CompletableFuture<HeartbeatResult> sendHeartbeat() {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                String heartbeatUrl = configManager.getHeartbeatUrl();

                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(heartbeatUrl))
                        .timeout(Duration.ofSeconds(5))
                        .GET()
                        .build();

                HttpResponse<String> response = httpClient.send(request,
                        HttpResponse.BodyHandlers.ofString());
                long responseTime = System.currentTimeMillis() - startTime;

                int statusCode = response.statusCode();
                String responseBody = response.body();

                boolean success = statusCode >= 200 && statusCode < 300;

                return new HeartbeatResult(success, statusCode, responseTime, responseBody);
            } catch (Exception e) {
                long responseTime = System.currentTimeMillis() - startTime;
                logger.error("心跳请求异常");
                return new HeartbeatResult(false, -1, responseTime, e.getMessage());
            }
        });
    }

    /**
     * 关闭HTTP客户端
     */
    public void close() {
        // Java 11+ HttpClient 不需要显式关闭
        logger.info("NetworkService 已关闭");
    }

    /**
     * 心跳结果数据类
     */
    public static class HeartbeatResult {
        private final boolean success;
        private final int statusCode;
        private final long responseTime;
        private final String message;

        public HeartbeatResult(boolean success, int statusCode, long responseTime, String message) {
            this.success = success;
            this.statusCode = statusCode;
            this.responseTime = responseTime;
            this.message = message;
        }

        public boolean isSuccess() {
            return success;
        }

        public int getStatusCode() {
            return statusCode;
        }

        public long getResponseTime() {
            return responseTime;
        }

        public String getMessage() {
            return message;
        }
    }
}
